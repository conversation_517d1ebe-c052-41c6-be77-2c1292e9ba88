import 'package:equatable/equatable.dart';
import 'package:quycky/app/features/friendship/domain/entities/chat_message_entity.dart';

class ChatStoreEntity extends Equatable {
  final Map<String, int> unreadMessagesCount;
  final List<ChatMessageEntity> currentMessages;
  final bool hasNewMessages;

  const ChatStoreEntity({
    this.unreadMessagesCount = const {},
    this.currentMessages = const [],
    this.hasNewMessages = false,
  });

  ChatStoreEntity copyWith({
    Map<String, int>? unreadMessagesCount,
    List<ChatMessageEntity>? currentMessages,
    bool? hasNewMessages,
  }) {
    return ChatStoreEntity(
      unreadMessagesCount: unreadMessagesCount ?? this.unreadMessagesCount,
      currentMessages: currentMessages ?? this.currentMessages,
      hasNewMessages: hasNewMessages ?? this.hasNewMessages,
    );
  }

  @override
  List<Object> get props => [unreadMessagesCount, currentMessages, hasNewMessages];
}
