import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/game/domain/dto/game_message_dto.dart';
import 'package:quycky/core/enumerators/egame_action.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/services/game_socket/game_socket_store.dart';
import 'package:quycky/core/entities/abs_mappable.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

typedef GameSocketMessageHandler = void Function(GameMessageDTO message);

class GameSocketService {
  final GameSocketStore _socketStore;
  final UserStore _userStore;

  WebSocketChannel? _channel;
  String _userToken = '';
  final Map<EGameAction, List<GameSocketMessageHandler>> _messageHandlers = {};
  final List<VoidCallback> _connectionHandlers = [];
  final List<VoidCallback> _disconnectionHandlers = [];
  Timer? _reconnectTimer;
  bool _shouldReconnect = true;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;
  static const Duration _reconnectDelay = Duration(seconds: 3);

  GameSocketService(this._socketStore, this._userStore) {
    _userStore.observer(
      onState: (state) {
        if (state.user.id.isNotEmpty && !isConnected) {
          connect();
        } else if (state.user.id.isEmpty && isConnected) {
          disconnect();
        }
      },
    );
  }

  bool get isConnected => _socketStore.state.isConnected;
  bool get isConnecting => _socketStore.state.isConnecting;
  String get connectionId => _socketStore.state.connectionId;

  // Adiciona handler para tipos específicos de mensagem
  void addMessageHandler(EGameAction action, GameSocketMessageHandler handler) {
    _messageHandlers.putIfAbsent(action, () => []).add(handler);
  }

  // Remove handler para tipos específicos de mensagem
  void removeMessageHandler(
      EGameAction action, GameSocketMessageHandler handler) {
    _messageHandlers[action]?.remove(handler);
    if (_messageHandlers[action]?.isEmpty == true) {
      _messageHandlers.remove(action);
    }
  }

  // Adiciona handler para conexão estabelecida
  void addConnectionHandler(VoidCallback handler) {
    _connectionHandlers.add(handler);
  }

  // Remove handler de conexão
  void removeConnectionHandler(VoidCallback handler) {
    _connectionHandlers.remove(handler);
  }

  // Adiciona handler para desconexão
  void addDisconnectionHandler(VoidCallback handler) {
    _disconnectionHandlers.add(handler);
  }

  // Remove handler de desconexão
  void removeDisconnectionHandler(VoidCallback handler) {
    _disconnectionHandlers.remove(handler);
  }

  Future<void> connect() async {
    if (isConnected || isConnecting) return;

    try {
      _socketStore.setConnectionState(SocketConnectionState.connecting);
      _userToken = await _userStore.getToken();

      if (_userToken.isEmpty) {
        throw Exception('User token is empty');
      }

      _channel = _createWebSocketChannel();
      await _channel!.ready;

      _socketStore.setConnectionState(SocketConnectionState.connected);
      _reconnectAttempts = 0;

      _configureSocket();
      _notifyConnectionHandlers();

      debugPrint('GameSocket: Connected successfully');
    } catch (e) {
      _socketStore.setSocketError('Connection failed: $e');
      debugPrint('GameSocket: Connection failed - $e');
      _scheduleReconnect();
    }
  }

  void disconnect() {
    _shouldReconnect = false;
    _reconnectTimer?.cancel();
    _reconnectTimer = null;

    try {
      _channel?.sink.close();
    } catch (e) {
      debugPrint('GameSocket: Error closing channel - $e');
    }

    _channel = null;
    _socketStore.setConnectionState(SocketConnectionState.disconnected);
    _notifyDisconnectionHandlers();

    debugPrint('GameSocket: Disconnected');
  }

  void sendMessage(AbsMappable data) {
    if (!isConnected) {
      debugPrint('GameSocket: Cannot send message - not connected');
      return;
    }

    try {
      _channel!.sink.add(data.toJson());
    } catch (e) {
      debugPrint('GameSocket: Error sending message - $e');
      _socketStore.setSocketError('Failed to send message: $e');
    }
  }

  GameMessageDTO createMessage(EGameAction action,
      {AbsMappable? data, String? roomName}) {
    final res = data?.toMap() ?? roomName;
    return GameMessageDTO(
      authorization: _userToken,
      action: action,
      connectionId: connectionId,
      data: res,
    );
  }

  WebSocketChannel _createWebSocketChannel() {
    if (kIsWeb) {
      throw UnimplementedError('Web socket not implemented for web');
    } else {
      RemoteConfigStore remoteConfigStore = Modular.get<RemoteConfigStore>();
      final socketUrl = AppEnv.mode == EAppRunMode.production
          ? remoteConfigStore.state.productionGameServerUrl
          : remoteConfigStore.state.developmentGameServerUrl;

      return IOWebSocketChannel.connect(
        socketUrl,
        headers: {
          "authorization": _userToken,
          "identifier": _userStore.state.user.identifier
        },
      );
    }
  }

  void _configureSocket() {
    _channel!.stream.listen(
      (message) {
        _onMessageReceived(message);
      },
      onDone: () {
        debugPrint('GameSocket: Connection closed');
        if (_shouldReconnect) {
          _socketStore.setConnectionState(SocketConnectionState.disconnected);
          _scheduleReconnect();
        }
      },
      onError: (error) {
        debugPrint('GameSocket: Stream error - $error');
        _socketStore.setSocketError('Stream error: $error');
        if (_shouldReconnect) {
          _scheduleReconnect();
        }
      },
      cancelOnError: true,
    );
  }

  void _onMessageReceived(String message) {
    try {
      final serverMessage = GameMessageDTO.fromJson(message);

      // Atualiza connectionId se recebido
      if (serverMessage.connectionId.isNotEmpty) {
        _socketStore.setConnectionId(serverMessage.connectionId);
      }

      // Notifica handlers específicos para esta ação
      final handlers = _messageHandlers[serverMessage.action];
      if (handlers != null) {
        for (final handler in handlers) {
          handler(serverMessage);
        }
      }
    } catch (e) {
      debugPrint('GameSocket: Error parsing message - $e');
    }
  }

  void _scheduleReconnect() {
    if (!_shouldReconnect || _reconnectAttempts >= _maxReconnectAttempts) {
      debugPrint(
          'GameSocket: Max reconnect attempts reached or reconnect disabled');
      return;
    }

    _reconnectAttempts++;
    _reconnectTimer = Timer(_reconnectDelay, () {
      debugPrint(
          'GameSocket: Attempting reconnect ($_reconnectAttempts/$_maxReconnectAttempts)');
      connect();
    });
  }

  void _notifyConnectionHandlers() {
    for (final handler in _connectionHandlers) {
      try {
        handler();
      } catch (e) {
        debugPrint('GameSocket: Error in connection handler - $e');
      }
    }
  }

  void _notifyDisconnectionHandlers() {
    for (final handler in _disconnectionHandlers) {
      try {
        handler();
      } catch (e) {
        debugPrint('GameSocket: Error in disconnection handler - $e');
      }
    }
  }

  void dispose() {
    _shouldReconnect = false;
    _reconnectTimer?.cancel();
    disconnect();
    _messageHandlers.clear();
    _connectionHandlers.clear();
    _disconnectionHandlers.clear();
  }
}
